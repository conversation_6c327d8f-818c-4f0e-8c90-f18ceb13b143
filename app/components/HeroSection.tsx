'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { FileText, Shield, Clock, DollarSign } from 'lucide-react';
import AddressAutocomplete from './AddressAutocomplete';
import { AutoCompleteResult } from '@/app/types';

interface HeroSectionProps {
  onAddressSelect: (address: AutoCompleteResult) => void;
}

export default function HeroSection({ onAddressSelect }: HeroSectionProps) {
  const features = [
    {
      icon: FileText,
      title: 'Professional Forms',
      description: 'Access state-specific forms and documents',
    },
    {
      icon: Shield,
      title: 'Legally Compliant',
      description: 'Ensure your offers meet legal requirements',
    },
    {
      icon: Clock,
      title: 'Save Time',
      description: 'Generate agreements in minutes, not hours',
    },
    {
      icon: DollarSign,
      title: 'Affordable',
      description: 'Professional agreements without agent fees',
    },
  ];

  return (
    <div className="bg-gradient-to-br from-primary-50 to-primary-100 min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center"
        >
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Professional Purchase Agreements
            <span className="block text-primary-600">Without an Agent</span>
          </h1>
          
          <p className="text-xl text-gray-700 mb-8 max-w-3xl mx-auto">
            Wholesalers, Section 8 investors, and buyers can now create legally compliant 
            as-is purchase agreements in minutes. No agent required.
          </p>

          <div className="max-w-2xl mx-auto mb-12">
            <div className="bg-white p-8 rounded-xl shadow-xl">
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                Get Started Now
              </h2>
              <p className="text-gray-600 mb-6">
                Enter a property address or paste a listing URL to begin
              </p>
              <AddressAutocomplete onSelect={onAddressSelect} />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-16">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white p-6 rounded-lg shadow-md"
              >
                <feature.icon className="w-12 h-12 text-primary-600 mb-4 mx-auto" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            ))}
          </div>

          <div className="mt-16 bg-white rounded-xl shadow-xl p-8">
            <h3 className="text-2xl font-semibold text-gray-900 mb-6">
              Simple Pricing
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900 mb-2">Free</div>
                <p className="text-gray-600 mb-4">Partial Agreement</p>
                <ul className="text-sm text-gray-500 space-y-2">
                  <li>Purchase agreement only</li>
                  <li>No seller name included</li>
                  <li>Perfect for initial interest</li>
                </ul>
              </div>
              <div className="text-center border-2 border-primary-500 rounded-lg p-4">
                <div className="text-3xl font-bold text-primary-600 mb-2">$5</div>
                <p className="text-gray-600 mb-4">Complete Agreement</p>
                <ul className="text-sm text-gray-500 space-y-2">
                  <li>All required forms</li>
                  <li>Seller name included</li>
                  <li>Ready to submit</li>
                </ul>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900 mb-2">$15/mo</div>
                <p className="text-gray-600 mb-4">Basic Subscription</p>
                <ul className="text-sm text-gray-500 space-y-2">
                  <li>25 official agreements</li>
                  <li>25 unofficial agreements</li>
                  <li>Priority support</li>
                </ul>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}