'use client';

import { useState, useEffect, useCallback } from 'react';
import { Search, Loader2 } from 'lucide-react';
import { AutoCompleteResult } from '@/app/types';
import { debounce } from '@/app/lib/utils';

interface AddressAutocompleteProps {
  onSelect: (address: AutoCompleteResult) => void;
  placeholder?: string;
}

export default function AddressAutocomplete({ onSelect, placeholder }: AddressAutocompleteProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<AutoCompleteResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);

  const searchAddresses = useCallback(async (searchQuery: string) => {
    if (searchQuery.length < 3) {
      setResults([]);
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/address/autocomplete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: searchQuery }),
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Autocomplete response data:', data);
        setResults(data.results || []);
        console.log('Set results:', data.results || []);
      } else {
        console.error('API response not ok:', response.status, response.statusText);

        // If API fails, try to create a manual entry for the user
        if (searchQuery.length > 10) {
          const manualResult: AutoCompleteResult = {
            id: 'manual-entry',
            address: searchQuery,
            city: '',
            state: '',
            zipCode: '',
            fullAddress: searchQuery,
          };
          setResults([manualResult]);
        } else {
          setResults([]);
        }
      }
    } catch (error) {
      console.error('Error searching addresses:', error);

      // Provide fallback manual entry option
      if (searchQuery.length > 10) {
        const manualResult: AutoCompleteResult = {
          id: 'manual-entry',
          address: searchQuery,
          city: '',
          state: '',
          zipCode: '',
          fullAddress: searchQuery,
        };
        setResults([manualResult]);
      } else {
        setResults([]);
      }
    } finally {
      setLoading(false);
    }
  }, []);

  const debouncedSearch = useCallback(
    debounce((value: string) => searchAddresses(value), 300),
    [searchAddresses]
  );

  useEffect(() => {
    console.log('Query changed:', query, 'Length:', query.length);
    if (query.length >= 3) {
      console.log('Triggering search for:', query);
      debouncedSearch(query);
    } else {
      setResults([]);
    }
  }, [query, debouncedSearch]);

  const handleSelect = (result: AutoCompleteResult) => {
    setQuery(result.fullAddress);
    setShowResults(false);
    onSelect(result);
  };

  return (
    <div className="relative">
      <div className="relative">
        <input
          type="text"
          value={query}
          onChange={(e) => {
            setQuery(e.target.value);
            setShowResults(true);
          }}
          onFocus={() => setShowResults(true)}
          placeholder={placeholder || "Enter property address or paste Zillow/Realtor.com URL"}
          className="w-full px-4 py-3 pl-12 pr-4 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
        />
        <div className="absolute inset-y-0 left-0 flex items-center pl-3">
          {loading ? (
            <Loader2 className="w-5 h-5 text-gray-400 animate-spin" />
          ) : (
            <Search className="w-5 h-5 text-gray-400" />
          )}
        </div>
      </div>

      {showResults && results.length > 0 && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-auto">
          {results.map((result) => (
            <button
              key={result.id}
              onClick={() => handleSelect(result)}
              className="w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none"
            >
              <p className="text-sm font-medium text-gray-900">{result.address}</p>
              <p className="text-sm text-gray-500">
                {result.city}, {result.state} {result.zipCode}
              </p>
            </button>
          ))}
        </div>
      )}

      {/* Debug info - remove in production */}
      {process.env.NODE_ENV === 'development' && (
        <div className="text-xs text-gray-500 mt-1">
          Results: {results.length}, Show: {showResults.toString()}, Query: "{query}"
          <button
            onClick={() => searchAddresses('123 main st')}
            className="ml-2 px-2 py-1 bg-blue-500 text-white text-xs rounded"
          >
            Test Search
          </button>
        </div>
      )}
    </div>
  );
}