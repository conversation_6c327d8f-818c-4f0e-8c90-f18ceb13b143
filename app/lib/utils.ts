import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

export function formatDate(date: string | Date): string {
  const d = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(d);
}

export function extractAddressFromUrl(url: string): string | null {
  // Extract address from Zillow or other real estate listing URLs
  try {
    // Check if it's actually a URL
    if (!url.includes('http') && !url.includes('www.')) {
      return null;
    }

    const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);

    // Zillow URL patterns
    if (urlObj.hostname.includes('zillow.com')) {
      const pathParts = urlObj.pathname.split('/');

      // Pattern: /homedetails/123-Main-St-City-State-12345/123456_zpid/
      const addressPart = pathParts.find(part =>
        part.includes('-') &&
        (part.includes('_zpid') || pathParts.indexOf(part) === pathParts.length - 2)
      );

      if (addressPart) {
        // Remove _zpid suffix and convert dashes to spaces
        const cleanAddress = addressPart
          .replace(/_zpid.*$/, '')
          .replace(/-/g, ' ')
          .replace(/\b\d{5}\b/, match => ` ${match}`) // Add space before ZIP
          .trim();

        return cleanAddress;
      }
    }

    // Realtor.com URL patterns
    if (urlObj.hostname.includes('realtor.com')) {
      const pathParts = urlObj.pathname.split('/');
      const addressPart = pathParts.find(part =>
        part.includes('_') && part.length > 10
      );

      if (addressPart) {
        return addressPart
          .replace(/_/g, ' ')
          .replace(/-/g, ' ')
          .trim();
      }
    }

    // Redfin URL patterns
    if (urlObj.hostname.includes('redfin.com')) {
      const pathParts = urlObj.pathname.split('/');
      const addressPart = pathParts.find(part =>
        part.includes('-') && part.length > 10
      );

      if (addressPart) {
        return addressPart
          .replace(/-/g, ' ')
          .replace(/\b\d+\b$/, match => ` ${match}`) // Add space before numbers at end
          .trim();
      }
    }

    return null;
  } catch (error) {
    console.error('Error extracting address from URL:', error);
    return null;
  }
}

export function generateDraftId(): string {
  return `draft_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}