import jsPDF from 'jspdf';
import { FormData, RequiredForm } from '@/app/types';
import { formatCurrency, formatDate } from './utils';
import { isDevMode, getStateName } from './forms';

// Helper function to get financing details based on payment type
function getFinancingDetails(paymentType: string): string {
  switch (paymentType) {
    case 'CASH':
      return 'This is an all-cash transaction with no financing contingency.';
    case 'CONVENTIONAL':
      return 'Buyer shall obtain conventional financing. This Agreement is contingent upon Buyer obtaining loan approval within 30 days.';
    case 'FHA':
      return 'Buyer shall obtain FHA financing. This Agreement is contingent upon Buyer obtaining FHA loan approval and property meeting FHA requirements.';
    case 'VA':
      return 'Buyer shall obtain VA financing. This Agreement is contingent upon Buyer obtaining VA loan approval and property meeting VA requirements.';
    case 'DSCR':
      return 'Buyer shall obtain DSCR (Debt Service Coverage Ratio) financing for investment property.';
    default:
      return 'Financing terms to be determined.';
  }
}

export function generatePurchaseAgreement(
  formData: FormData,
  includeSellerName: boolean,
  includeForms: boolean
): jsPDF {
  const doc = new jsPDF();
  let yPosition = 20;
  
  // Get state code from property address
  const stateCode = formData.propertyDetails?.address?.state?.toUpperCase() || '';
  const stateName = getStateName(stateCode);

  // Header
  doc.setFontSize(20);
  doc.setFont('helvetica', 'bold');
  doc.text(`${stateName.toUpperCase()} AS-IS PURCHASE AGREEMENT`, 105, yPosition, { align: 'center' });
  yPosition += 15;

  // Add dev mode indicator if enabled
  if (isDevMode()) {
    doc.setFontSize(12);
    doc.setTextColor(255, 0, 0);
    doc.text('DEVELOPER TEST MODE - NOT FOR PRODUCTION USE', 105, yPosition, { align: 'center' });
    doc.setTextColor(0, 0, 0);
    yPosition += 10;
  }

  // Agreement Date
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');
  doc.text(`Date: ${new Date().toLocaleDateString()}`, 20, yPosition);
  yPosition += 15;

  // PARTIES Section
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('1. PARTIES', 20, yPosition);
  yPosition += 10;
  
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');
  doc.text('This Purchase Agreement ("Agreement") is entered into between:', 20, yPosition);
  yPosition += 10;
  
  // Buyer Information
  doc.setFont('helvetica', 'bold');
  doc.text('BUYER:', 20, yPosition);
  doc.setFont('helvetica', 'normal');
  yPosition += 6;
  doc.text(`Name: ${formData.buyerInfo?.name || ''}`, 30, yPosition);
  yPosition += 6;
  doc.text(`Email: ${formData.buyerInfo?.email || ''}`, 30, yPosition);
  yPosition += 6;
  if (formData.buyerInfo?.phone) {
    doc.text(`Phone: ${formData.buyerInfo.phone}`, 30, yPosition);
    yPosition += 6;
  }
  
  // Seller Information
  yPosition += 4;
  doc.setFont('helvetica', 'bold');
  doc.text('SELLER:', 20, yPosition);
  doc.setFont('helvetica', 'normal');
  yPosition += 6;
  if (includeSellerName) {
    doc.text('Name: _________________________________', 30, yPosition);
    yPosition += 6;
    doc.text('Address: _________________________________', 30, yPosition);
  } else {
    doc.text('Name: [NAME REDACTED UNTIL AGREEMENT ACCEPTED]', 30, yPosition);
    yPosition += 6;
    doc.text('Address: [TO BE PROVIDED]', 30, yPosition);
  }
  
  yPosition += 15;

  // PROPERTY Section
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('2. PROPERTY', 20, yPosition);
  yPosition += 10;
  
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');
  doc.text('The real property and improvements located at:', 20, yPosition);
  yPosition += 8;
  doc.text(formData.propertyDetails?.address?.fullAddress || '', 30, yPosition);
  yPosition += 8;
  
  if (formData.propertyDetails?.legalDescription) {
    doc.text(`Legal Description: ${formData.propertyDetails.legalDescription}`, 30, yPosition);
    yPosition += 8;
  }
  
  if (formData.propertyDetails?.yearBuilt) {
    doc.text(`Year Built: ${formData.propertyDetails.yearBuilt}`, 30, yPosition);
    yPosition += 8;
  }

  yPosition += 10;

  // PURCHASE PRICE Section
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('3. PURCHASE PRICE', 20, yPosition);
  yPosition += 10;
  
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');
  doc.text(`Buyer agrees to purchase the Property for ${formatCurrency(formData.offerDetails?.offerPrice || 0)}`, 20, yPosition);
  yPosition += 15;

  // EARNEST MONEY Section
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('4. EARNEST MONEY', 20, yPosition);
  yPosition += 10;
  
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');
  const earnestText = `Buyer shall deposit ${formatCurrency(formData.offerDetails?.earnestDeposit || 0)} as earnest money within ${formData.offerDetails?.earnestDepositDays || 3} business days`;
  doc.text(earnestText, 20, yPosition);
  yPosition += 8;
  doc.text(`with ${formData.offerDetails?.titleCompany || '[TITLE COMPANY]'} as Escrow Agent.`, 20, yPosition);
  yPosition += 15;

  // FINANCING Section
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('5. FINANCING', 20, yPosition);
  yPosition += 10;
  
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');
  doc.text(`Payment Type: ${formData.offerDetails?.paymentType || 'CASH'}`, 20, yPosition);
  yPosition += 8;
  
  const financingDetails = getFinancingDetails(formData.offerDetails?.paymentType || 'CASH');
  const splitFinancing = doc.splitTextToSize(financingDetails, 170);
  splitFinancing.forEach((line: string) => {
    doc.text(line, 20, yPosition);
    yPosition += 6;
  });
  
  // Check if we need a new page
  if (yPosition > 240) {
    doc.addPage();
    yPosition = 20;
  }

  yPosition += 10;

  // CLOSING Section
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('6. CLOSING', 20, yPosition);
  yPosition += 10;
  
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');
  const closingDate = formData.offerDetails?.closingDate 
    ? formatDate(formData.offerDetails.closingDate)
    : '[TO BE DETERMINED]';
  doc.text(`Closing shall occur on or before ${closingDate}`, 20, yPosition);
  yPosition += 15;

  // INSPECTION PERIOD Section
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('7. INSPECTION PERIOD', 20, yPosition);
  yPosition += 10;
  
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');
  const inspectionText = `Buyer shall have ${formData.offerDetails?.inspectionDays || 10} days to conduct inspections. During this period, Buyer may terminate this Agreement for any reason.`;
  const splitInspection = doc.splitTextToSize(inspectionText, 170);
  splitInspection.forEach((line: string) => {
    doc.text(line, 20, yPosition);
    yPosition += 6;
  });

  yPosition += 10;

  // AS-IS CONDITION Section
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('8. AS-IS CONDITION', 20, yPosition);
  yPosition += 10;
  
  doc.setFontSize(11);
  doc.setFont('helvetica', 'bold');
  const asIsText = 'BUYER ACKNOWLEDGES THAT THE PROPERTY IS BEING SOLD "AS IS, WHERE IS, WITH ALL FAULTS."';
  const splitAsIs = doc.splitTextToSize(asIsText, 170);
  splitAsIs.forEach((line: string) => {
    doc.text(line, 20, yPosition);
    yPosition += 6;
  });
  
  doc.setFont('helvetica', 'normal');
  yPosition += 4;
  const asIsDetails = 'Seller makes no warranties or representations concerning the Property\'s condition.';
  doc.text(asIsDetails, 20, yPosition);
  
  // Check if we need a new page for state-specific provisions
  if (yPosition > 220) {
    doc.addPage();
    yPosition = 20;
  } else {
    yPosition += 15;
  }

  // State-Specific Provisions
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text(`9. ${stateName.toUpperCase()} SPECIFIC PROVISIONS`, 20, yPosition);
  yPosition += 10;
  
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');
  
  // Add basic state-specific provisions
  addStateSpecificProvisions(doc, stateCode, yPosition);
  
  // If partial agreement, add note about additional forms
  if (!includeForms && formData.requiredForms && formData.requiredForms.length > 0) {
    doc.addPage();
    yPosition = 20;
    
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('ADDITIONAL FORMS REQUIRED', 20, yPosition);
    yPosition += 10;
    
    doc.setFontSize(11);
    doc.setFont('helvetica', 'normal');
    doc.text('If this initial offer is accepted, the following forms will be provided:', 20, yPosition);
    yPosition += 10;
    
    formData.requiredForms.forEach((form) => {
      doc.text(`• ${form}`, 25, yPosition);
      yPosition += 7;
    });
  }

  // Signature Page
  doc.addPage();
  yPosition = 20;
  
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('SIGNATURES', 20, yPosition);
  yPosition += 20;
  
  // Buyer Signature
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');
  doc.line(20, yPosition, 90, yPosition);
  yPosition += 5;
  doc.text('Buyer Signature', 55, yPosition, { align: 'center' });
  yPosition += 10;
  doc.line(20, yPosition, 90, yPosition);
  yPosition += 5;
  doc.text('Date', 55, yPosition, { align: 'center' });
  
  // Seller Signature
  yPosition -= 15;
  doc.line(120, yPosition, 190, yPosition);
  yPosition += 5;
  doc.text('Seller Signature', 155, yPosition, { align: 'center' });
  yPosition += 10;
  doc.line(120, yPosition, 190, yPosition);
  yPosition += 5;
  doc.text('Date', 155, yPosition, { align: 'center' });

  return doc;
}

// Add state-specific provisions
function addStateSpecificProvisions(doc: jsPDF, stateCode: string, startY: number): number {
  let yPosition = startY;
  
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');
  
  // Add provisions based on state
  switch (stateCode) {
    case 'CA':
      doc.text('• Liquidated Damages: Limited to 3% of purchase price', 20, yPosition);
      yPosition += 6;
      doc.text('• Earthquake and environmental hazards disclosures required', 20, yPosition);
      yPosition += 6;
      doc.text('• Mediation required before litigation', 20, yPosition);
      break;
    case 'TX':
      doc.text('• Survey required unless waived', 20, yPosition);
      yPosition += 6;
      doc.text('• Mineral rights may be reserved', 20, yPosition);
      yPosition += 6;
      doc.text('• MUD/HOA disclosures if applicable', 20, yPosition);
      break;
    case 'FL':
      doc.text('• Radon gas disclosure provided', 20, yPosition);
      yPosition += 6;
      doc.text('• Coastal construction requirements if applicable', 20, yPosition);
      yPosition += 6;
      doc.text('• Homestead status disclosure required', 20, yPosition);
      break;
    case 'NY':
      doc.text('• Property condition disclosure or $500 credit', 20, yPosition);
      yPosition += 6;
      doc.text('• Lead paint disclosure if pre-1978', 20, yPosition);
      break;
    default:
      doc.text('• Property sold subject to state and local laws', 20, yPosition);
      yPosition += 6;
      doc.text('• All required state disclosures to be provided', 20, yPosition);
  }
  
  return yPosition + 10;
}

export function generateAdditionalForms(
  formData: FormData,
  requiredForms: RequiredForm[]
): jsPDF[] {
  const documents: jsPDF[] = [];
  
  requiredForms.forEach((form) => {
    // Skip the main purchase agreement
    if (form.id.includes('purchase-agreement')) return;
    
    const doc = new jsPDF();
    let yPosition = 20;
    
    // Form header
    doc.setFontSize(18);
    doc.setFont('helvetica', 'bold');
    doc.text(form.name.toUpperCase(), 105, yPosition, { align: 'center' });
    yPosition += 10;
    
    // Form description
    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text(form.description, 105, yPosition, { align: 'center' });
    yPosition += 15;
    
    // Add dev mode indicator if enabled
    if (isDevMode()) {
      doc.setFontSize(12);
      doc.setTextColor(255, 0, 0);
      doc.text('DEVELOPER TEST MODE', 105, yPosition, { align: 'center' });
      doc.setTextColor(0, 0, 0);
      yPosition += 10;
    }
    
    // Property Information
    yPosition += 10;
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('PROPERTY INFORMATION', 20, yPosition);
    yPosition += 10;
    
    doc.setFontSize(11);
    doc.setFont('helvetica', 'normal');
    doc.text(`Address: ${formData.propertyDetails?.address?.fullAddress || ''}`, 20, yPosition);
    yPosition += 10;
    
    // Form-specific content
    if (form.id === 'lead-paint-disclosure') {
      generateLeadPaintDisclosure(doc, formData, yPosition);
    } else if (form.id.includes('property-disclosure')) {
      generatePropertyDisclosure(doc, formData, yPosition);
    } else {
      // Generic form content
      doc.text('This form is required for your real estate transaction.', 20, yPosition);
      yPosition += 10;
      doc.text('Please consult with your real estate professional for specific requirements.', 20, yPosition);
    }
    
    // Signature section at bottom
    yPosition = 250;
    doc.line(20, yPosition, 90, yPosition);
    doc.text('Buyer Signature', 55, yPosition + 5, { align: 'center' });
    
    doc.line(120, yPosition, 190, yPosition);
    doc.text('Seller Signature', 155, yPosition + 5, { align: 'center' });
    
    documents.push(doc);
  });
  
  return documents;
}

function generateLeadPaintDisclosure(doc: jsPDF, formData: FormData, startY: number): void {
  let yPosition = startY;
  
  doc.setFontSize(11);
  doc.setFont('helvetica', 'bold');
  doc.text('LEAD WARNING STATEMENT', 20, yPosition);
  yPosition += 10;
  
  doc.setFont('helvetica', 'normal');
  const warning = 'Every purchaser of any interest in residential real property on which a residential dwelling was built prior to 1978 is notified that such property may present exposure to lead from lead-based paint.';
  const splitWarning = doc.splitTextToSize(warning, 170);
  splitWarning.forEach((line: string) => {
    doc.text(line, 20, yPosition);
    yPosition += 6;
  });
  
  yPosition += 10;
  doc.setFont('helvetica', 'bold');
  doc.text('SELLER\'S DISCLOSURE', 20, yPosition);
  yPosition += 8;
  
  doc.setFont('helvetica', 'normal');
  doc.text('[ ] Known lead-based paint and/or lead-based paint hazards are present', 20, yPosition);
  yPosition += 6;
  doc.text('[ ] Seller has no knowledge of lead-based paint and/or lead-based paint hazards', 20, yPosition);
  yPosition += 6;
  doc.text('[ ] Seller has no reports or records pertaining to lead-based paint', 20, yPosition);
}

function generatePropertyDisclosure(doc: jsPDF, formData: FormData, startY: number): void {
  let yPosition = startY;
  
  doc.setFontSize(11);
  doc.setFont('helvetica', 'bold');
  doc.text('PROPERTY CONDITION DISCLOSURE', 20, yPosition);
  yPosition += 10;
  
  doc.setFont('helvetica', 'normal');
  doc.text('The following are representations made by the seller and are not verified by the buyer.', 20, yPosition);
  yPosition += 10;
  
  // Basic disclosure items
  const items = [
    'Structural Systems',
    'Roof',
    'Plumbing System',
    'Electrical System',
    'HVAC System',
    'Appliances',
    'Foundation'
  ];
  
  items.forEach((item) => {
    doc.text(`${item}: [ ] No Known Problems [ ] Problems (explain below)`, 20, yPosition);
    yPosition += 8;
  });
}

export async function savePurchaseAgreement(
  formData: FormData,
  includeSellerName: boolean,
  includeForms: boolean
): Promise<string> {
  const doc = generatePurchaseAgreement(formData, includeSellerName, includeForms);
  
  // Generate filename
  const timestamp = new Date().toISOString().slice(0, 10);
  const buyerName = formData.buyerInfo?.name.replace(/\s+/g, '_') || 'buyer';
  const agreementType = formData.agreementType || 'agreement';
  const filename = `${agreementType}_${buyerName}_${timestamp}.pdf`;
  
  // Save the PDF
  doc.save(filename);
  
  return filename;
}
